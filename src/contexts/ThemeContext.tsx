import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react'

export type Theme = 'light' | 'dark'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  isSystemTheme: boolean
  setSystemTheme: (useSystem: boolean) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: ReactNode
}

const STORAGE_KEY = 'portfolio-theme'
const SYSTEM_PREFERENCE_KEY = 'portfolio-use-system-theme'

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isSystemTheme, setIsSystemTheme] = useState<boolean>(() => {
    const saved = localStorage.getItem(SYSTEM_PREFERENCE_KEY)
    return saved ? JSON.parse(saved) : true
  })

  const [theme, setTheme] = useState<Theme>(() => {
    // If using system theme, check system preference
    if (isSystemTheme) {
      return window.matchMedia?.('(prefers-color-scheme: light)').matches ? 'light' : 'dark'
    }

    // Otherwise, check localStorage
    const savedTheme = localStorage.getItem(STORAGE_KEY) as Theme
    return savedTheme || 'dark' // Default to dark theme (Return of the Jedi Luke)
  })

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    setIsSystemTheme(false)
    localStorage.setItem(STORAGE_KEY, newTheme)
    localStorage.setItem(SYSTEM_PREFERENCE_KEY, 'false')
  }, [theme])

  const handleSetSystemTheme = useCallback((useSystem: boolean) => {
    setIsSystemTheme(useSystem)
    localStorage.setItem(SYSTEM_PREFERENCE_KEY, JSON.stringify(useSystem))

    if (useSystem) {
      const systemTheme = window.matchMedia?.('(prefers-color-scheme: light)').matches ? 'light' : 'dark'
      setTheme(systemTheme)
      localStorage.removeItem(STORAGE_KEY)
    }
  }, [])

  // Listen for system theme changes
  useEffect(() => {
    if (!isSystemTheme) return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: light)')
    const handleChange = (e: MediaQueryListEvent) => {
      setTheme(e.matches ? 'light' : 'dark')
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [isSystemTheme])

  useEffect(() => {
    // Apply theme class to document root
    document.documentElement.setAttribute('data-theme', theme)

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme === 'light' ? '#f8f9fa' : '#0a0a0a')
    }

    // Update CSS custom properties for better performance
    const root = document.documentElement
    if (theme === 'light') {
      root.style.setProperty('--theme-transition', 'all 0.3s ease')
    } else {
      root.style.setProperty('--theme-transition', 'all 0.3s ease')
    }
  }, [theme])

  const value = {
    theme,
    toggleTheme,
    isSystemTheme,
    setSystemTheme: handleSetSystemTheme,
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}
