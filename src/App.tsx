import React, { useEffect, Suspense, lazy } from 'react'
import '@styles/App.scss'
import Container from '@mui/material/Container'
import { ThemeProvider } from '@contexts/ThemeContext'
import ErrorBoundary from '@components/ErrorBoundary'
import LoadingSpinner from '@components/LoadingSpinner'
import Nav from '@components/Nav'

// Lazy load components for better performance
const Introduction = lazy(() => import('@components/Introduction'))
const AboutMe = lazy(() => import('@components/AboutMe'))
const Skills = lazy(() => import('@components/Skills'))
const ProjectsSection = lazy(() => import('@components/ProjectsSection'))
const ContactMe = lazy(() => import('@components/ContactMe'))
const Footer = lazy(() => import('@components/Footer'))

const App: React.FC = () => {
  // Start at the top of the page
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  // Easter egg for developers
  useEffect(() => {
    console.log(
      "🌟 These aren't the droids you're looking for... but this portfolio might be what you seek! 🌟"
    )
    console.log('Built with React 18, TypeScript, and the Force ⚡')
  }, [])

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <div className='App'>
          <Container component='main' className='contactContainer'>
            <Nav />
            <Suspense fallback={<LoadingSpinner />}>
              <Introduction />
              <AboutMe />
              <Skills />
              <ProjectsSection />
              <ContactMe />
            </Suspense>
          </Container>
          <Suspense fallback={<div>Loading footer...</div>}>
            <Footer />
          </Suspense>
        </div>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
