import { describe, it, expect, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import App from '../../App'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock typewriter-effect
vi.mock('typewriter-effect', () => ({
  default: ({ options }: any) => <span>{options.strings?.[0] || 'Typewriter'}</span>,
}))

// Mock emailjs
vi.mock('@emailjs/browser', () => ({
  send: vi.fn().mockResolvedValue({ status: 200 }),
  init: vi.fn(),
}))

describe('App Component', () => {
  it('renders without crashing', async () => {
    render(<App />)

    // Wait for lazy-loaded components
    await waitFor(() => {
      expect(screen.getByText(/A long time ago, in a galaxy far, far away/i)).toBeInTheDocument()
    })
  })

  it('displays the main navigation', async () => {
    render(<App />)

    await waitFor(() => {
      // Check for navigation elements
      const lukeAvatar = screen.getByAltText('Luke')
      expect(lukeAvatar).toBeInTheDocument()
    })
  })

  it('shows loading spinner initially', () => {
    render(<App />)

    // Should show loading spinner for lazy components
    expect(screen.getByText(/Loading.../i)).toBeInTheDocument()
  })

  it('has theme toggle functionality', async () => {
    const user = userEvent.setup()
    render(<App />)

    await waitFor(() => {
      // Look for theme toggle button
      const themeButton = screen.getByLabelText(/theme options/i)
      expect(themeButton).toBeInTheDocument()
    })
  })

  it('displays Star Wars themed content', async () => {
    render(<App />)

    await waitFor(() => {
      // Check for Star Wars references
      expect(screen.getByText(/galaxy far, far away/i)).toBeInTheDocument()
      expect(screen.getByText(/Jedi software engineer/i)).toBeInTheDocument()
    })
  })

  it('has proper accessibility attributes', async () => {
    render(<App />)

    await waitFor(() => {
      // Check for proper ARIA labels
      const themeButton = screen.getByLabelText(/theme options/i)
      expect(themeButton).toHaveAttribute('aria-label')
    })
  })

  it('handles errors gracefully', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // This would trigger error boundary in a real error scenario
    render(<App />)

    // In a real test, you'd throw an error in a child component
    // and verify the error boundary displays

    consoleSpy.mockRestore()
  })

  it('preloads critical resources', async () => {
    render(<App />)

    await waitFor(() => {
      // Check that images are loaded
      const lukeImage = screen.getByAltText('Luke')
      expect(lukeImage).toHaveAttribute('src')
    })
  })
})
