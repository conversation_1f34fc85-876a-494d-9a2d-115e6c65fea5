import React from 'react';

interface DeathStarLoaderProps {
    size?: number;
    color?: string;
}

const DeathStarLoader: React.FC<DeathStarLoaderProps> = ({ 
    size = 60, 
    color = '#00ff41' 
}) => {
    return (
        <div 
            className="death-star-loader"
            style={{
                width: size,
                height: size,
                position: 'relative',
                margin: '20px auto',
            }}
        >
            {/* Main sphere */}
            <div
                style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    border: `2px solid ${color}`,
                    position: 'relative',
                    animation: 'death-star-rotate 3s linear infinite',
                    background: `radial-gradient(circle at 30% 30%, rgba(0, 255, 65, 0.1), transparent 50%)`,
                    boxShadow: `0 0 20px ${color}40`,
                }}
            >
                {/* Superlaser dish */}
                <div
                    style={{
                        width: '20%',
                        height: '20%',
                        borderRadius: '50%',
                        border: `1px solid ${color}`,
                        position: 'absolute',
                        top: '25%',
                        left: '25%',
                        background: color,
                        boxShadow: `0 0 10px ${color}`,
                        animation: 'superlaser-pulse 1.5s ease-in-out infinite',
                    }}
                />
                
                {/* Surface details */}
                <div
                    style={{
                        width: '8%',
                        height: '8%',
                        borderRadius: '50%',
                        border: `1px solid ${color}80`,
                        position: 'absolute',
                        top: '60%',
                        left: '70%',
                    }}
                />
                <div
                    style={{
                        width: '6%',
                        height: '6%',
                        borderRadius: '50%',
                        border: `1px solid ${color}60`,
                        position: 'absolute',
                        top: '15%',
                        left: '65%',
                    }}
                />
                <div
                    style={{
                        width: '4%',
                        height: '4%',
                        borderRadius: '50%',
                        border: `1px solid ${color}40`,
                        position: 'absolute',
                        top: '75%',
                        left: '20%',
                    }}
                />
            </div>
            
            {/* Orbital rings */}
            <div
                style={{
                    width: '120%',
                    height: '120%',
                    borderRadius: '50%',
                    border: `1px solid ${color}30`,
                    position: 'absolute',
                    top: '-10%',
                    left: '-10%',
                    animation: 'orbital-ring 4s linear infinite reverse',
                }}
            />
            <div
                style={{
                    width: '140%',
                    height: '140%',
                    borderRadius: '50%',
                    border: `1px solid ${color}20`,
                    position: 'absolute',
                    top: '-20%',
                    left: '-20%',
                    animation: 'orbital-ring 6s linear infinite',
                }}
            />
        </div>
    );
};

export default DeathStarLoader;
