import React, { useState } from 'react'
import { Icon<PERSON>utton, Toolt<PERSON>, Menu, MenuItem, Typography, Divider } from '@mui/material'
import { motion, AnimatePresence } from 'framer-motion'
import { useTheme } from '@contexts/ThemeContext'

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme, isSystemTheme, setSystemTheme } = useTheme()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [isHovered, setIsHovered] = useState(false)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleToggle = () => {
    toggleTheme()
    handleClose()
  }

  const handleSystemTheme = () => {
    setSystemTheme(true)
    handleClose()
  }

  const getThemeIcon = () => {
    if (isSystemTheme) return '🖥️'
    return theme === 'light' ? '🌙' : '☀️'
  }

  const getTooltipText = () => {
    if (isSystemTheme) return 'Using System Theme'
    return theme === 'light' ? 'Switch to Dark Side' : 'Join the Light Side'
  }

  return (
    <>
      <Tooltip title={getTooltipText()} arrow placement="left">
        <motion.div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 1300,
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <IconButton
            onClick={handleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className="theme-toggle"
            aria-label="Theme options"
            sx={{
              width: 56,
              height: 56,
              borderRadius: '50%',
              background: theme === 'light'
                ? 'rgba(26, 26, 46, 0.95)'
                : 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: theme === 'light'
                ? '2px solid #3498db'
                : '2px solid #00ff41',
              boxShadow: theme === 'light'
                ? '0 0 20px rgba(52, 152, 219, 0.5)'
                : '0 0 20px rgba(0, 255, 65, 0.3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                boxShadow: theme === 'light'
                  ? '0 0 30px rgba(52, 152, 219, 0.8)'
                  : '0 0 30px rgba(0, 255, 65, 0.6)',
                transform: 'translateY(-2px)',
              }
            }}
          >
            <motion.div
              animate={{
                rotate: isHovered ? 360 : 0,
                scale: isHovered ? 1.1 : 1
              }}
              transition={{
                duration: 0.5,
                ease: "easeInOut"
              }}
              style={{
                fontSize: '24px',
                filter: theme === 'light'
                  ? 'drop-shadow(0 0 8px rgba(52, 152, 219, 0.8))'
                  : 'drop-shadow(0 0 8px rgba(0, 255, 65, 0.8))',
              }}
            >
              {getThemeIcon()}
            </motion.div>
          </IconButton>
        </motion.div>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            background: theme === 'light'
              ? 'rgba(255, 255, 255, 0.95)'
              : 'rgba(26, 26, 46, 0.95)',
            backdropFilter: 'blur(10px)',
            border: theme === 'light'
              ? '1px solid rgba(52, 152, 219, 0.3)'
              : '1px solid rgba(0, 255, 65, 0.3)',
            borderRadius: '12px',
            minWidth: 200,
            mt: 1,
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Typography
          variant="subtitle2"
          sx={{
            px: 2,
            py: 1,
            color: 'var(--text-primary)',
            fontFamily: '"Space Mono", monospace',
            fontWeight: 600,
          }}
        >
          Choose Your Path
        </Typography>
        <Divider sx={{ borderColor: 'var(--border-color)' }} />

        <MenuItem
          onClick={handleSystemTheme}
          selected={isSystemTheme}
          sx={{
            color: 'var(--text-primary)',
            fontFamily: '"Space Mono", monospace',
            '&:hover': {
              background: 'var(--accent-primary)',
              color: 'var(--bg-primary)',
            }
          }}
        >
          🖥️ System Preference
        </MenuItem>

        <MenuItem
          onClick={handleToggle}
          sx={{
            color: 'var(--text-primary)',
            fontFamily: '"Space Mono", monospace',
            '&:hover': {
              background: 'var(--accent-primary)',
              color: 'var(--bg-primary)',
            }
          }}
        >
          {theme === 'light' ? '🌙 Dark Side' : '☀️ Light Side'}
        </MenuItem>
      </Menu>
    </>
  )
}

export default ThemeToggle
