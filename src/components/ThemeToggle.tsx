import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
    const { theme, toggleTheme } = useTheme();

    return (
        <Tooltip
            title={theme === 'light' ? 'Switch to Dark Side' : 'Join the Light Side'}
            arrow
        >
            <IconButton
                onClick={toggleTheme}
                className="theme-toggle"
                aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
                style={{
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    zIndex: 1300, // Higher than MUI AppBar (1100)
                    width: '56px',
                    height: '56px',
                    borderRadius: '50%',
                    background: theme === 'light'
                        ? 'rgba(26, 26, 46, 0.95)'
                        : 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    border: theme === 'light'
                        ? '2px solid #3498db'
                        : '2px solid #00ff41',
                    boxShadow: theme === 'light'
                        ? '0 0 20px rgba(52, 152, 219, 0.5)'
                        : '0 0 20px rgba(0, 255, 65, 0.3)',
                    transition: 'all 0.3s ease',
                }}
            >
                <div
                    style={{
                        fontSize: '24px',
                        transition: 'all 0.3s ease',
                        filter: theme === 'light'
                            ? 'drop-shadow(0 0 8px rgba(52, 152, 219, 0.8))'
                            : 'drop-shadow(0 0 8px rgba(0, 255, 65, 0.8))',
                    }}
                >
                    {theme === 'light' ? '🌙' : '☀️'}
                </div>
            </IconButton>
        </Tooltip>
    );
};

export default ThemeToggle;
