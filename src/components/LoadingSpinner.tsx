import React from 'react'
import { motion } from 'framer-motion'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  message?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  message = 'Loading...' 
}) => {
  const sizeMap = {
    small: '40px',
    medium: '60px',
    large: '80px'
  }

  const spinnerSize = sizeMap[size]

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '2rem',
        minHeight: '200px',
      }}
    >
      {/* Death Star Loading Spinner */}
      <motion.div
        style={{
          width: spinnerSize,
          height: spinnerSize,
          borderRadius: '50%',
          background: `
            radial-gradient(circle at 30% 30%, #666 20%, transparent 21%),
            linear-gradient(135deg, #333 0%, #666 50%, #333 100%)
          `,
          border: '2px solid var(--accent-primary)',
          position: 'relative',
          boxShadow: '0 0 20px var(--shadow-color)',
        }}
        animate={{
          rotate: 360,
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        {/* Death Star superlaser dish */}
        <div
          style={{
            position: 'absolute',
            top: '25%',
            left: '25%',
            width: '20%',
            height: '20%',
            borderRadius: '50%',
            background: 'var(--accent-primary)',
            boxShadow: '0 0 10px var(--accent-primary)',
          }}
        />
        
        {/* Death Star surface details */}
        <div
          style={{
            position: 'absolute',
            top: '10%',
            left: '60%',
            width: '3px',
            height: '30%',
            background: 'var(--accent-primary)',
            opacity: 0.6,
          }}
        />
        <div
          style={{
            position: 'absolute',
            top: '60%',
            left: '10%',
            width: '30%',
            height: '3px',
            background: 'var(--accent-primary)',
            opacity: 0.6,
          }}
        />
      </motion.div>

      {/* Loading text with typing effect */}
      <motion.p
        style={{
          marginTop: '1rem',
          color: 'var(--text-primary)',
          fontFamily: '"Space Mono", monospace',
          fontSize: '1rem',
          textShadow: '0 0 5px var(--shadow-color)',
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {message}
      </motion.p>

      {/* Pulsing dots */}
      <div
        style={{
          display: 'flex',
          gap: '4px',
          marginTop: '0.5rem',
        }}
      >
        {[0, 1, 2].map(index => (
          <motion.div
            key={index}
            style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: 'var(--accent-primary)',
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default LoadingSpinner
