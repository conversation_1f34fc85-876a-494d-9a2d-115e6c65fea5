import React, { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }

    // You can also log the error to an error reporting service here
    // logErrorToService(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default fallback UI with Star Wars theme
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
            color: '#e0e0e0',
            fontFamily: '"Space Mono", monospace',
            textAlign: 'center',
            padding: '2rem',
          }}
        >
          <div
            style={{
              fontSize: '4rem',
              marginBottom: '1rem',
              color: '#00ff41',
              textShadow: '0 0 20px #00ff41',
            }}
          >
            ⚠️
          </div>
          <h1
            style={{
              fontSize: '2rem',
              marginBottom: '1rem',
              color: '#00ff41',
              textShadow: '0 0 10px #00ff41',
            }}
          >
            A Disturbance in the Force
          </h1>
          <p
            style={{
              fontSize: '1.2rem',
              marginBottom: '2rem',
              maxWidth: '600px',
              lineHeight: '1.6',
            }}
          >
            Something went wrong in this galaxy far, far away. The Death Star may have interfered
            with our systems.
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '12px 24px',
              fontSize: '1rem',
              background: 'transparent',
              border: '2px solid #00ff41',
              color: '#00ff41',
              borderRadius: '8px',
              cursor: 'pointer',
              fontFamily: '"Space Mono", monospace',
              transition: 'all 0.3s ease',
              boxShadow: '0 0 10px rgba(0, 255, 65, 0.3)',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.background = '#00ff41'
              e.currentTarget.style.color = '#0a0a0a'
              e.currentTarget.style.boxShadow = '0 0 20px rgba(0, 255, 65, 0.6)'
            }}
            onMouseLeave={e => {
              e.currentTarget.style.background = 'transparent'
              e.currentTarget.style.color = '#00ff41'
              e.currentTarget.style.boxShadow = '0 0 10px rgba(0, 255, 65, 0.3)'
            }}
          >
            Restart the Mission
          </button>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details
              style={{
                marginTop: '2rem',
                padding: '1rem',
                background: 'rgba(255, 0, 0, 0.1)',
                border: '1px solid rgba(255, 0, 0, 0.3)',
                borderRadius: '8px',
                maxWidth: '800px',
                textAlign: 'left',
              }}
            >
              <summary style={{ cursor: 'pointer', marginBottom: '1rem' }}>
                Error Details (Development Only)
              </summary>
              <pre
                style={{
                  fontSize: '0.8rem',
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                }}
              >
                {this.state.error.toString()}
              </pre>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
