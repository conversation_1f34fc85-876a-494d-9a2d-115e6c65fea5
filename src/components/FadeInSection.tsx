import React, { ReactNode, useEffect, useRef, useState, memo } from 'react'
import { motion } from 'framer-motion'

interface FadeInSectionProps {
  children: ReactNode
  delay?: string | number
  className?: string
  threshold?: number
  once?: boolean
  direction?: 'up' | 'down' | 'left' | 'right'
  distance?: number
}

const FadeInSection: React.FC<FadeInSectionProps> = memo(({
  children,
  delay = 0,
  className = '',
  threshold = 0.1,
  once = true,
  direction = 'up',
  distance = 50,
}) => {
  const [isVisible, setVisible] = useState(false)
  const domRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setVisible(true)
            if (once && domRef.current) {
              observer.unobserve(domRef.current)
            }
          } else if (!once) {
            setVisible(false)
          }
        })
      },
      { threshold }
    )

    const currentRef = domRef.current
    if (currentRef) {
      observer.observe(currentRef)
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef)
      }
    }
  }, [threshold, once])

  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { y: distance }
      case 'down':
        return { y: -distance }
      case 'left':
        return { x: distance }
      case 'right':
        return { x: -distance }
      default:
        return { y: distance }
    }
  }

  const delayValue = typeof delay === 'string' ? parseFloat(delay) / 1000 : delay

  return (
    <motion.div
      ref={domRef}
      className={`fade-in-section ${isVisible ? 'is-visible' : ''} ${className}`}
      initial={{
        opacity: 0,
        ...getInitialPosition(),
      }}
      animate={{
        opacity: isVisible ? 1 : 0,
        x: isVisible ? 0 : getInitialPosition().x || 0,
        y: isVisible ? 0 : getInitialPosition().y || 0,
      }}
      transition={{
        duration: 0.8,
        delay: delayValue,
        ease: [0.25, 0.46, 0.45, 0.94], // Custom easing
      }}
    >
      {children}
    </motion.div>
  )
})

FadeInSection.displayName = 'FadeInSection'

export default FadeInSection
