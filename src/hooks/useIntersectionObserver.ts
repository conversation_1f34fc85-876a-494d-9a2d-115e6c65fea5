import { useEffect, useRef, useState, useCallback } from 'react'

interface UseIntersectionObserverOptions extends IntersectionObserverInit {
  freezeOnceVisible?: boolean
}

export const useIntersectionObserver = (
  options: UseIntersectionObserverOptions = {}
): [React.RefObject<HTMLDivElement>, boolean] => {
  const { threshold = 0.1, root = null, rootMargin = '0%', freezeOnceVisible = false } = options

  const elementRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  const callbackFunction = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries
      const isElementVisible = entry.isIntersecting

      setIsVisible(isElementVisible)

      if (isElementVisible && freezeOnceVisible) {
        // Disconnect observer once element becomes visible
        if (elementRef.current) {
          observer.unobserve(elementRef.current)
        }
      }
    },
    [freezeOnceVisible]
  )

  let observer: IntersectionObserver

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    observer = new IntersectionObserver(callbackFunction, {
      threshold,
      root,
      rootMargin,
    })

    observer.observe(element)

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [callbackFunction, threshold, root, rootMargin])

  return [elementRef, isVisible]
}

export default useIntersectionObserver
