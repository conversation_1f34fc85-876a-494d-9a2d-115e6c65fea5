// Typography
$font-primary: 'Space Mono', 'JetBrains Mono', monospace;
$font-heading: 'Orbitron', 'Space Mono', monospace;
$font-code: 'Fira Code', 'JetBrains Mono', monospace;

// Breakpoints
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1200px;

// Z-index layers
$z-index-background: -1;
$z-index-base: 1;
$z-index-elevated: 10;
$z-index-modal: 100;
$z-index-tooltip: 1000;

// Transitions
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.6s ease;

// Shadows
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 16px;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-xxl: 3rem;

// Mixins
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin hover-glow($color: var(--accent-primary)) {
  transition: all $transition-normal;
  
  &:hover {
    filter: drop-shadow(0 0 10px $color);
    transform: translateY(-2px);
  }
}

@mixin glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Responsive mixins
@mixin mobile {
  @media (max-width: $breakpoint-mobile) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: $breakpoint-tablet) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-desktop) {
    @content;
  }
}
