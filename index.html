<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#0a0a0a" />
    <meta name="description" content="<PERSON> - Full Stack Software Engineer Portfolio. Explore my projects and skills in modern web development." />
    <meta name="keywords" content="<PERSON>, Software Engineer, Full Stack Developer, React, TypeScript, Portfolio" />
    <meta name="author" content="<PERSON>" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://lukebanicevic.github.io/" />
    <meta property="og:title" content="<PERSON> | Software Engineer" />
    <meta property="og:description" content="Full Stack Software Engineer Portfolio - Explore my projects and skills" />
    <meta property="og:image" content="/profile.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://lukebanicevic.github.io/" />
    <meta property="twitter:title" content="Luke Banicevic | Software Engineer" />
    <meta property="twitter:description" content="Full Stack Software Engineer Portfolio - Explore my projects and skills" />
    <meta property="twitter:image" content="/profile.png" />

    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />

    <title>Luke Banicevic | Software Engineer</title>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: monospace; color: #00ff41; background: #0a0a0a; height: 100vh;">
        <h1>🌟 Use the Force, Luke! 🌟</h1>
        <p>This portfolio requires JavaScript to be enabled in your browser.</p>
        <p>Please enable JavaScript and reload the page to explore the galaxy of code.</p>
      </div>
    </noscript>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
