{"name": "portfolio-website", "version": "0.2.0", "private": true, "type": "module", "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@mui/system": "^6.1.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "framer-motion": "^11.15.0", "gh-pages": "^6.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.0", "react-transition-group": "^4.4.5", "sass": "^1.82.0", "typewriter-effect": "^2.21.0", "typescript": "^5.8.3", "web-vitals": "^4.2.4"}, "homepage": "/", "scripts": {"start": "vite", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "prepare": "husky install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-transition-group": "^4.4.11", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "vite": "^6.0.5", "vite-plugin-pwa": "^0.21.1", "vite-plugin-sass": "^0.1.0", "vitest": "^2.1.8"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}}