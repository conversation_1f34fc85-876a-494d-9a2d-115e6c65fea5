{"name": "portfolio-website", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^3.4.0", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.1.16", "@mui/icons-material": "^5.3.1", "@mui/material": "^5.4.0", "@mui/styles": "^5.3.0", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.4.0", "@types/node": "^16.11.19", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "babel-runtime": "^6.26.0", "gh-pages": "^3.2.3", "sass": "^1.77.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^6.15.0", "react-scripts": "5.0.0", "react-typed": "^1.2.0", "react-typist": "^2.0.5", "typescript": "^4.5.4", "web-vitals": "^2.1.3"}, "homepage": "/", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-fontawesome": "^1.6.5", "@types/react-typing-animation": "^1.6.3", "@types/react-typist": "^2.0.3"}}