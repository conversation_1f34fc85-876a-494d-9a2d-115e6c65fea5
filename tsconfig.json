{"compilerOptions": {"target": "ES2022", "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "verbatimModuleSyntax": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@assets/*": ["./src/assets/*"], "@styles/*": ["./src/styles/*"], "@constants/*": ["./src/constants/*"], "@contexts/*": ["./src/contexts/*"], "@utils/*": ["./src/utils/*"], "@hooks/*": ["./src/hooks/*"]}, "types": ["vite/client", "vitest/globals", "@testing-library/jest-dom"]}, "include": ["src", "vite.config.ts", "vitest.config.ts"], "exclude": ["node_modules", "dist", "coverage"], "references": [{"path": "./tsconfig.node.json"}]}